[tox]
envlist = py{27,34,35,36},flake8

[testenv]
usedevelop = true
setenv =
    PYTHONPATH = {toxinidir}:{toxinidir}/rfc5424logging
deps =
    pytest
    pytest-cov
    coverage
    mock
commands =
    coverage erase
    py.test --verbose --cov=./rfc5424logging --cov-report= {posargs}
    coverage report -m
    coverage html

[testenv:flake8]
deps =
    flake8
    pep8-naming
commands =
    flake8 rfc5424logging