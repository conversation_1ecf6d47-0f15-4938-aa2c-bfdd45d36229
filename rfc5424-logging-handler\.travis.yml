# Config file for automatic testing at travis-ci.org

language: python
matrix:
    include:
        - python: 2.7
          env: TOXENV=py27
        - python: 3.4
          env: TOXENV=py34
        - python: 3.5
          env: TOXENV=py35
        - python: 3.6
          env: TOXENV=py36
        - python: 3.5
          env: TOXENV=flake8

# command to install dependencies for getting Tox running, other dependencies are installed by Tox
before_install:
    - pip install codecov tox

# command to run tests, e.g. python setup.py test
script:
    - tox

after_success:
    - codecov -e TOXENV
